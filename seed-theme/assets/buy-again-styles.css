/* Buy Again Magic Link & Order History Styles */

/* Common Variables */
:root {
  --buy-again-primary: #00BFFF;
  --buy-again-primary-hover: #0099CC;
  --buy-again-success: #28a745;
  --buy-again-error: #dc3545;
  --buy-again-text: #333;
  --buy-again-text-light: #666;
  --buy-again-text-lighter: #888;
  --buy-again-border: #e1e5e9;
  --buy-again-bg: #f8f9fa;
  --buy-again-white: #ffffff;
  --buy-again-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --buy-again-shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.15);
  --buy-again-radius: 12px;
  --buy-again-radius-small: 8px;
}

/* Enhanced Buy Again Magic Link Styles */
.buy-again-magic-link-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.buy-again-magic-link-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  opacity: 0.3;
}

.buy-again-container {
  position: relative;
  z-index: 1;
}

.buy-again-form-wrapper {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.buy-again-form-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: var(--buy-again-shadow-hover);
}

.buy-again-icon svg {
  filter: drop-shadow(0 2px 4px rgba(0, 191, 255, 0.3));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.buy-again-email-input {
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.buy-again-email-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 191, 255, 0.2);
}

.buy-again-submit-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.buy-again-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.buy-again-submit-btn:hover::before {
  left: 100%;
}

.buy-again-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
}

/* Enhanced Order History Styles */
.order-history-wrapper {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.order-history-title {
  background: linear-gradient(135deg, var(--buy-again-primary), #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.order-item {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.order-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--buy-again-shadow-hover);
  border-color: rgba(0, 191, 255, 0.2);
}

.order-header {
  position: relative;
}

.order-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--buy-again-border), transparent);
}

.order-content {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-image {
  position: relative;
  overflow: hidden;
  border-radius: var(--buy-again-radius-small);
}

.product-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.order-item:hover .product-image::before {
  transform: translateX(100%);
}

.color-dot {
  transition: all 0.2s ease;
  cursor: pointer;
}

.color-dot:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.buy-again-btn, .reorder-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.buy-again-btn::before, .reorder-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.buy-again-btn:hover::before, .reorder-btn:hover::before {
  width: 300px;
  height: 300px;
}

.buy-again-btn:hover, .reorder-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 191, 255, 0.3);
}

.order-offer {
  position: relative;
  padding: 10px;
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-radius: var(--buy-again-radius-small);
  border: 1px solid #ffeaa7;
}

.order-offer::before {
  content: '🎉';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 16px;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.view-more-btn {
  position: relative;
  transition: all 0.2s ease;
}

.view-more-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--buy-again-primary);
  transition: width 0.3s ease;
}

.view-more-btn:hover::after {
  width: 100%;
}

/* Loading States */
.buy-again-submit-btn:disabled,
.buy-again-btn:disabled,
.reorder-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

.buy-again-submit-btn:disabled::before,
.buy-again-btn:disabled::before,
.reorder-btn:disabled::before {
  display: none;
}

/* Success/Error Message Animations */
.success-message, .error-message {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .buy-again-form-wrapper {
    margin: 0 10px;
  }
  
  .order-item {
    margin: 0 10px;
  }
  
  .order-actions {
    padding: 15px;
  }
  
  .buy-again-btn, .reorder-btn {
    font-size: 16px;
    padding: 15px 20px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --buy-again-text: #e9ecef;
    --buy-again-text-light: #adb5bd;
    --buy-again-text-lighter: #6c757d;
    --buy-again-bg: #212529;
    --buy-again-white: #343a40;
    --buy-again-border: #495057;
  }
  
  .buy-again-form-wrapper {
    background: rgba(52, 58, 64, 0.95);
  }
  
  .order-item {
    background: var(--buy-again-white);
  }
}
