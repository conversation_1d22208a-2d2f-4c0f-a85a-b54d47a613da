<div style="min-height: 60vh; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: {{ section.settings.spacing_desktop | default: 80 }}px 20px;">
  <div style="max-width: 500px; width: 100%; margin: 0 auto;">
    <div style="background: white; border-radius: 12px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); padding: 40px; text-align: center;">

      <div style="margin-bottom: 20px;">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#00BFFF"/>
        </svg>
      </div>

      <h1 style="font-size: 28px; font-weight: 600; color: #333; margin-bottom: 10px;">
        {{ section.settings.title | default: 'Welcome back!' }}
      </h1>

      <p style="font-size: 16px; color: #666; margin-bottom: 20px;">
        {{ section.settings.subtitle | default: 'Enter your email below to get a login link' }}
      </p>

      <div style="margin-bottom: 30px;">
        <p style="font-size: 14px; color: #888; line-height: 1.5;">
          {{ section.settings.description | default: 'We already got your foot imprints, so we can begin to craft your custom orthotics right away.' }}
        </p>
      </div>

      <form id="buyAgainForm" style="margin-bottom: 20px;">
        <div style="margin-bottom: 20px;">
          <input
            type="email"
            id="customer_email"
            name="customer[email]"
            placeholder="Your email"
            style="width: 100%; padding: 15px 20px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; box-sizing: border-box;"
            required
          >
        </div>

        <button type="submit" style="width: 100%; background: #00BFFF; color: white; border: none; padding: 15px 20px; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer;">
          {{ section.settings.button_text | default: 'SEND ME A LINK' }}
        </button>
      </form>

      <div>
        <div id="successMessage" style="display: none; background: #d4edda; color: #155724; padding: 12px; border-radius: 6px; border: 1px solid #c3e6cb; margin-top: 20px;">
          {{ section.settings.success_message | default: 'Check your email for a login link!' }}
        </div>
        <div id="errorMessage" style="display: none; background: #f8d7da; color: #721c24; padding: 12px; border-radius: 6px; border: 1px solid #f5c6cb; margin-top: 20px;">
          {{ section.settings.error_message | default: 'Please enter a valid email address.' }}
        </div>
      </div>

    </div>
  </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('buyAgainForm');
  const successMessage = document.getElementById('successMessage');
  const errorMessage = document.getElementById('errorMessage');

  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const email = document.getElementById('customer_email').value.trim();

      // Hide previous messages
      successMessage.style.display = 'none';
      errorMessage.style.display = 'none';

      // Basic email validation
      if (!email || !isValidEmail(email)) {
        errorMessage.style.display = 'block';
        return;
      }

      // Show success message
      successMessage.style.display = 'block';

      // Redirect to order history after 2 seconds
      setTimeout(function() {
        window.location.href = '/pages/order-history?email=' + encodeURIComponent(email);
      }, 2000);
    });
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
});
</script>



{% schema %}
{
  "name": "Buy Again Magic Link",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Welcome back!"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Enter your email below to get a login link"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "We already got your foot imprints, so we can begin to craft your custom orthotics right away."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SEND ME A LINK"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success Message",
      "default": "Check your email for a login link!"
    },
    {
      "type": "text",
      "id": "error_message",
      "label": "Error Message",
      "default": "Please enter a valid email address."
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
