<!-- BUY AGAIN MAGIC LINK SECTION LOADED -->
<div style="padding: 60px 20px; background: #f0f0f0; min-height: 400px;">
  <div style="max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; text-align: center;">

    <h1 style="color: #333; font-size: 32px; margin-bottom: 20px;">
      {{ section.settings.title | default: 'Welcome back!' }}
    </h1>

    <p style="color: #666; font-size: 18px; margin-bottom: 30px;">
      {{ section.settings.subtitle | default: 'Enter your email below to get a login link' }}
    </p>

    <form id="buyAgainForm">
      <input
        type="email"
        id="customer_email"
        placeholder="Your email address"
        style="width: 100%; max-width: 400px; padding: 15px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; margin-bottom: 20px; box-sizing: border-box;"
        required
      >
      <br>
      <button
        type="submit"
        style="background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; font-size: 16px; cursor: pointer;"
      >
        {{ section.settings.button_text | default: 'SEND ME A LINK' }}
      </button>
    </form>

    <div id="message" style="margin-top: 20px; padding: 10px; border-radius: 5px; display: none;"></div>

  </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('buyAgainForm');
  const message = document.getElementById('message');

  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const email = document.getElementById('customer_email').value;

      if (email && email.includes('@')) {
        message.style.display = 'block';
        message.style.background = '#d4edda';
        message.style.color = '#155724';
        message.textContent = 'Check your email for a login link!';

        setTimeout(function() {
          window.location.href = '/pages/order-history';
        }, 2000);
      } else {
        message.style.display = 'block';
        message.style.background = '#f8d7da';
        message.style.color = '#721c24';
        message.textContent = 'Please enter a valid email address.';
      }
    });
  }
});
</script>



{% schema %}
{
  "name": "Buy Again Magic Link",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Welcome back!"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Enter your email below to get a login link"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "We already got your foot imprints, so we can begin to craft your custom orthotics right away."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SEND ME A LINK"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success Message",
      "default": "Check your email for a login link!"
    },
    {
      "type": "text",
      "id": "error_message",
      "label": "Error Message",
      "default": "Please enter a valid email address."
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
