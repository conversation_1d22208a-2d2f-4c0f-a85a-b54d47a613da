{{ 'buy-again-styles.css' | asset_url | stylesheet_tag }}

<div class="buy-again-magic-link-wrapper" style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="buy-again-container">
    <div class="buy-again-form-wrapper">
      <div class="buy-again-content">
        <div class="buy-again-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#00BFFF"/>
          </svg>
        </div>
        
        <h1 class="buy-again-title">{{ section.settings.title }}</h1>
        <p class="buy-again-subtitle">{{ section.settings.subtitle }}</p>
        
        <div class="buy-again-description">
          <p>{{ section.settings.description }}</p>
        </div>
        
        <form class="buy-again-form" id="buyAgainForm" action="#" method="post">
          <div class="form-group">
            <label for="customer_email" class="sr-only">Your email</label>
            <input 
              type="email" 
              id="customer_email" 
              name="customer[email]" 
              placeholder="Your email"
              class="buy-again-email-input"
              required
            >
          </div>
          
          <button type="submit" class="buy-again-submit-btn">
            {{ section.settings.button_text }}
          </button>
        </form>
        
        <div class="buy-again-messages">
          <div class="success-message" id="successMessage" style="display: none;">
            {{ section.settings.success_message }}
          </div>
          <div class="error-message" id="errorMessage" style="display: none;">
            {{ section.settings.error_message }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.buy-again-magic-link-wrapper {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.buy-again-container {
  max-width: 500px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

.buy-again-form-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.buy-again-icon {
  margin-bottom: 20px;
}

.buy-again-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.buy-again-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.buy-again-description {
  margin-bottom: 30px;
}

.buy-again-description p {
  font-size: 14px;
  color: #888;
  line-height: 1.5;
}

.buy-again-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.buy-again-email-input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.buy-again-email-input:focus {
  outline: none;
  border-color: #00BFFF;
}

.buy-again-submit-btn {
  width: 100%;
  background: #00BFFF;
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.buy-again-submit-btn:hover {
  background: #0099CC;
}

.buy-again-messages {
  margin-top: 20px;
}

.success-message {
  background: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #c3e6cb;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
}

@media (max-width: 768px) {
  .buy-again-magic-link-wrapper {
    padding-top: {{ section.settings.spacing_mobile }}px;
    padding-bottom: {{ section.settings.spacing_mobile }}px;
  }
  
  .buy-again-form-wrapper {
    padding: 30px 20px;
  }
  
  .buy-again-title {
    font-size: 24px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('buyAgainForm');
  const successMessage = document.getElementById('successMessage');
  const errorMessage = document.getElementById('errorMessage');
  const submitBtn = form ? form.querySelector('.buy-again-submit-btn') : null;

  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const email = document.getElementById('customer_email').value.trim();

      // Hide previous messages
      successMessage.style.display = 'none';
      errorMessage.style.display = 'none';

      // Basic email validation
      if (!email || !isValidEmail(email)) {
        showError('Please enter a valid email address.');
        return;
      }

      // Show loading state
      if (submitBtn) {
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;
      }

      // Simulate API call to check customer and send magic link
      checkCustomerAndSendMagicLink(email)
        .then(function(response) {
          if (response.success) {
            showSuccess('Check your email for a login link!');

            // Store email in sessionStorage for order history page
            sessionStorage.setItem('buyAgainEmail', email);

            // Redirect after showing success message
            setTimeout(function() {
              if (response.hasOrders) {
                window.location.href = '/pages/order-history?email=' + encodeURIComponent(email);
              } else {
                showError('No orders found for this email address.');
                resetButton();
              }
            }, 2000);
          } else {
            showError(response.message || 'Something went wrong. Please try again.');
            resetButton();
          }
        })
        .catch(function(error) {
          console.error('Magic link error:', error);
          showError('Unable to send magic link. Please try again.');
          resetButton();
        });
    });
  }

  function checkCustomerAndSendMagicLink(email) {
    // In a real implementation, this would make an API call to your backend
    // which would:
    // 1. Check if customer exists in Shopify
    // 2. Check if customer has orders
    // 3. Send magic link email
    // 4. Return appropriate response

    return new Promise(function(resolve) {
      // Simulate API delay
      setTimeout(function() {
        // For demo purposes, simulate different responses based on email
        if (email.includes('test') || email.includes('demo')) {
          resolve({
            success: true,
            hasOrders: true,
            customerId: 'demo-customer-id'
          });
        } else if (email.includes('noorders')) {
          resolve({
            success: false,
            message: 'No orders found for this email address.'
          });
        } else {
          // Default: assume customer exists and has orders
          resolve({
            success: true,
            hasOrders: true,
            customerId: 'customer-' + Date.now()
          });
        }
      }, 1500);
    });
  }

  function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
  }

  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
  }

  function resetButton() {
    if (submitBtn) {
      submitBtn.textContent = '{{ section.settings.button_text }}';
      submitBtn.disabled = false;
    }
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Handle URL parameters (for when user comes back from email link)
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  const email = urlParams.get('email');

  if (token && email) {
    // User clicked magic link - validate token and redirect to order history
    validateMagicLinkToken(token, email)
      .then(function(response) {
        if (response.valid) {
          sessionStorage.setItem('buyAgainEmail', email);
          sessionStorage.setItem('buyAgainToken', token);
          window.location.href = '/pages/order-history?authenticated=true';
        } else {
          showError('Invalid or expired link. Please try again.');
        }
      })
      .catch(function(error) {
        console.error('Token validation error:', error);
        showError('Unable to validate link. Please try again.');
      });
  }

  function validateMagicLinkToken(token, email) {
    // In a real implementation, this would validate the token with your backend
    return new Promise(function(resolve) {
      setTimeout(function() {
        // For demo, assume token is valid if it exists
        resolve({ valid: true });
      }, 500);
    });
  }
});
</script>

{% schema %}
{
  "name": "Buy Again Magic Link",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Welcome back!"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Enter your email below to get a login link"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "We already got your foot imprints, so we can begin to craft your custom orthotics right away."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SEND ME A LINK"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success Message",
      "default": "Check your email for a login link!"
    },
    {
      "type": "text",
      "id": "error_message",
      "label": "Error Message",
      "default": "Please enter a valid email address."
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
