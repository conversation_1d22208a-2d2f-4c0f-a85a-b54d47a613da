<!-- BUY AGAIN MAGIC LINK SECTION LOADED -->
<div style="padding: 60px 20px; background: white; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
  <div class="buy-again-container" style="max-width: 500px; width: 100%; text-align: center; padding: 60px 20px;">

    <h1 class="buy-again-title" style="color: #4A4A4A; font-family: Lato, sans-serif; font-size: 40px; font-weight: 700; line-height: normal; letter-spacing: 0.098px; margin-bottom: 20px;">
      {{ section.settings.title | default: 'Welcome back!' }}
    </h1>

    <p style="color: #6E6E6E; text-align: center; font-family: Lato, sans-serif; font-size: 16px; font-weight: 700; line-height: normal; letter-spacing: 0.098px; margin-bottom: 30px;">
      {{ section.settings.subtitle | default: 'Enter your email below to get a login link' }}
    </p>

    <div style="margin-bottom: 20px;">
      <svg xmlns="http://www.w3.org/2000/svg" width="31" height="28" viewBox="0 0 31 28" fill="none">
        <path clip-rule="evenodd" d="M18.325 24.241c-1.28 1.318-1.92 1.978-2.722 1.978-.803.001-1.444-.657-2.726-1.973l-3.059-3.139-5.74-5.937a8.51 8.51 0 0 1 0-11.7 7.6 7.6 0 0 1 11.104.616l.422.414.417-.432a7.6 7.6 0 0 1 11.103-.615 8.51 8.51 0 0 1 0 11.699l-5.74 5.937z" stroke="#1AA8E3" stroke-width="2.217" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>

    <p style="color: #A5A5A5; text-align: center; font-family: Lato, sans-serif; font-size: 16px; font-weight: 700; line-height: normal; letter-spacing: 0.098px; margin-bottom: 40px;">
      We already got your foot imprints, so we can begin to craft your custom orthotics right away.
    </p>

    <form id="buyAgainForm" style="display: flex; flex-direction: column; gap: 20px;">
      <input
        type="email"
        id="customer_email"
        class="buy-again-input"
        placeholder="Your email"
        style="display: flex; padding: 14px 24px; flex-direction: column; justify-content: center; align-items: flex-start; gap: 32px; align-self: stretch; border-radius: 30px; border: 1px solid #B7B7B7; background: #FFF; font-family: Lato, sans-serif; font-size: 16px; box-sizing: border-box; outline: none; transition: all 0.3s ease;"
        required
      >

      <button
        type="submit"
        class="buy-again-button"
        style="display: flex; height: 51px; padding: 9px 20px; justify-content: center; align-items: center; gap: 6px; align-self: stretch; border-radius: 30px; background: #00A2F5; box-shadow: 0 4px 16px 0 rgba(0, 158, 224, 0.40); border: none; color: white; font-family: Lato, sans-serif; font-size: 16px; font-weight: 700; cursor: pointer; transition: all 0.3s ease;"
      >
        {{ section.settings.button_text | default: 'SEND ME A LINK' }}
      </button>
    </form>

    <div id="message" style="margin-top: 20px; padding: 10px; border-radius: 5px; display: none; font-family: Lato, sans-serif;"></div>

  </div>
</div>


<style>
.buy-again-button:hover {
  background: #0091D9 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(0, 158, 224, 0.50) !important;
}

.buy-again-input:focus {
  border-color: #1AA8E3 !important;
  box-shadow: 0 0 0 3px rgba(26, 168, 227, 0.1);
}

@media (max-width: 768px) {
  .buy-again-title {
    font-size: 32px !important;
  }

  .buy-again-container {
    padding: 40px 20px !important;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('buyAgainForm');
  const message = document.getElementById('message');
  const button = form ? form.querySelector('button[type="submit"]') : null;

  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const email = document.getElementById('customer_email').value.trim();

      // Hide previous message
      message.style.display = 'none';

      if (email && email.includes('@') && email.includes('.')) {
        // Show loading state
        if (button) {
          button.textContent = 'Sending...';
          button.disabled = true;
          button.style.opacity = '0.7';
        }

        // Show success message after delay
        setTimeout(function() {
          message.style.display = 'block';
          message.style.background = '#d4edda';
          message.style.color = '#155724';
          message.style.border = '1px solid #c3e6cb';
          message.style.borderRadius = '30px';
          message.style.padding = '12px 20px';
          message.textContent = 'Check your email for a login link!';

          // Redirect to order history
          setTimeout(function() {
            window.location.href = '/pages/order-history?email=' + encodeURIComponent(email);
          }, 2000);
        }, 1000);

      } else {
        message.style.display = 'block';
        message.style.background = '#f8d7da';
        message.style.color = '#721c24';
        message.style.border = '1px solid #f5c6cb';
        message.style.borderRadius = '30px';
        message.style.padding = '12px 20px';
        message.textContent = 'Please enter a valid email address.';
      }
    });
  }
});
</script>



{% schema %}
{
  "name": "Buy Again Magic Link",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Welcome back!"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Enter your email below to get a login link"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "We already got your foot imprints, so we can begin to craft your custom orthotics right away."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SEND ME A LINK"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success Message",
      "default": "Check your email for a login link!"
    },
    {
      "type": "text",
      "id": "error_message",
      "label": "Error Message",
      "default": "Please enter a valid email address."
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
