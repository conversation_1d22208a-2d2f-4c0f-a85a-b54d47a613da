{{ 'buy-again-styles.css' | asset_url | stylesheet_tag }}

<div class="order-history-wrapper" style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="order-history-container">
    <header class="order-history-header">
      <h1 class="order-history-title">{{ section.settings.title }}</h1>
      <p class="order-history-subtitle">{{ section.settings.subtitle }}</p>
    </header>

    {% comment %} This section will display customer orders when properly integrated {% endcomment %}
    {% if customer and customer.orders.size > 0 %}
      <div class="orders-list">
        {% for order in customer.orders limit: 10 %}
          <div class="order-item" data-order-id="{{ order.id }}">
            <div class="order-header">
              <div class="order-number">
                <span class="order-label">Order number</span>
                <span class="order-value">{{ order.name }}</span>
              </div>
              <button class="order-toggle" aria-expanded="false">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M6 9L1.5 4.5L2.5 3.5L6 7L9.5 3.5L10.5 4.5L6 9Z" fill="currentColor"/>
                </svg>
              </button>
            </div>
            
            <div class="order-content">
              {% for line_item in order.line_items limit: 1 %}
                <div class="order-product">
                  {% if line_item.image %}
                    <div class="product-image">
                      <img 
                        src="{{ line_item.image | image_url: width: 120, height: 120 }}" 
                        alt="{{ line_item.title }}"
                        width="60"
                        height="60"
                        loading="lazy"
                      >
                    </div>
                  {% endif %}
                  
                  <div class="product-details">
                    <h3 class="product-title">{{ line_item.title }}</h3>
                    <div class="product-variants">
                      {% unless line_item.product.has_only_default_variant %}
                        {% for option in line_item.options_with_values %}
                          <span class="variant-option">{{ option.name }}: {{ option.value }}</span>
                        {% endfor %}
                      {% endunless %}
                    </div>
                    <div class="product-meta">
                      <span class="product-size">Size: {{ line_item.variant.option1 | default: '8.5' }}</span>
                      <span class="product-pairs">Pairs: {{ line_item.variant.option2 | default: 'Right The forefoot' }}</span>
                    </div>
                  </div>
                </div>
              {% endfor %}
              
              <div class="order-actions">
                <div class="order-offer">
                  <span class="offer-text">Secret offer! Up to 75% off</span>
                  <span class="offer-code">Use code: Secret</span>
                </div>
                <button class="buy-again-btn" data-order-id="{{ order.id }}">
                  {{ section.settings.buy_again_text }}
                </button>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    {% else %}
      {% comment %} Demo orders for when customer is not logged in or has no orders {% endcomment %}
      <div class="orders-list">
        <div class="order-item" data-order-id="UP-000098782">
          <div class="order-header">
            <div class="order-number">
              <span class="order-label">Order number</span>
              <span class="order-value">UP-000098782</span>
            </div>
            <button class="order-toggle" aria-expanded="false">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M6 9L1.5 4.5L2.5 3.5L6 7L9.5 3.5L10.5 4.5L6 9Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
          
          <div class="order-content">
            <div class="order-product">
              <div class="product-image">
                <img 
                  src="https://via.placeholder.com/120x120/FF6B35/FFFFFF?text=Shoe" 
                  alt="On my feet all day"
                  width="60"
                  height="60"
                  loading="lazy"
                >
              </div>
              
              <div class="product-details">
                <h3 class="product-title">On my feet all day</h3>
                <div class="product-variants">
                  <span class="color-dot" style="background-color: #4A90E2;"></span>
                  <span class="color-dot" style="background-color: #F5A623;"></span>
                  <span class="color-dot" style="background-color: #7ED321;"></span>
                  <span class="color-dot" style="background-color: #333;"></span>
                </div>
                <div class="product-meta">
                  <span class="product-size">Size: 8.5</span>
                  <span class="product-pairs">Pairs: Right The forefoot</span>
                </div>
                <button class="view-more-btn">{{ section.settings.view_more_text }}</button>
              </div>
            </div>
            
            <div class="order-actions">
              <div class="order-offer">
                <span class="offer-text">Secret offer! Up to 75% off</span>
                <span class="offer-code">Use code: Secret</span>
              </div>
              <button class="buy-again-btn" data-order-id="UP-000098782">
                {{ section.settings.buy_again_text }}
              </button>
            </div>
          </div>
        </div>

        <div class="order-item" data-order-id="UP-000098742">
          <div class="order-header">
            <div class="order-number">
              <span class="order-label">Order number</span>
              <span class="order-value">UP-000098742</span>
            </div>
            <button class="order-toggle" aria-expanded="false">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M6 9L1.5 4.5L2.5 3.5L6 7L9.5 3.5L10.5 4.5L6 9Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
          
          <div class="order-content">
            <div class="order-product">
              <div class="product-image">
                <img 
                  src="https://via.placeholder.com/120x120/FF6B35/FFFFFF?text=Shoe" 
                  alt="On my feet all day"
                  width="60"
                  height="60"
                  loading="lazy"
                >
              </div>
              
              <div class="product-details">
                <h3 class="product-title">On my feet all day</h3>
                <div class="product-variants">
                  <span class="color-dot" style="background-color: #4A90E2;"></span>
                  <span class="color-dot" style="background-color: #F5A623;"></span>
                  <span class="color-dot" style="background-color: #7ED321;"></span>
                  <span class="color-dot" style="background-color: #333;"></span>
                </div>
                <div class="product-meta">
                  <span class="product-size">Size: 8.5</span>
                  <span class="product-pairs">Pairs: Right The forefoot</span>
                </div>
                <button class="view-more-btn">{{ section.settings.view_more_text }}</button>
              </div>
            </div>
            
            <div class="order-actions">
              <div class="order-offer">
                <span class="offer-text">Secret offer! Up to 75% off</span>
                <span class="offer-code">Use code: Secret</span>
              </div>
              <button class="buy-again-btn" data-order-id="UP-000098742">
                {{ section.settings.buy_again_text }}
              </button>
            </div>
          </div>
        </div>

        <div class="order-item" data-order-id="UP-000F">
          <div class="order-header">
            <div class="order-number">
              <span class="order-label">Order number</span>
              <span class="order-value">UP-000F</span>
            </div>
            <button class="order-toggle" aria-expanded="false">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M6 9L1.5 4.5L2.5 3.5L6 7L9.5 3.5L10.5 4.5L6 9Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
          
          <div class="order-content">
            <div class="order-product">
              <div class="product-image">
                <img 
                  src="https://via.placeholder.com/120x120/FF6B35/FFFFFF?text=Shoe" 
                  alt="On my feet all day"
                  width="60"
                  height="60"
                  loading="lazy"
                >
              </div>
              
              <div class="product-details">
                <h3 class="product-title">On my feet all day</h3>
                <div class="product-variants">
                  <span class="color-dot" style="background-color: #4A90E2;"></span>
                  <span class="color-dot" style="background-color: #F5A623;"></span>
                  <span class="color-dot" style="background-color: #7ED321;"></span>
                  <span class="color-dot" style="background-color: #333;"></span>
                </div>
                <div class="product-meta">
                  <span class="product-size">Size: 8.5</span>
                  <span class="product-pairs">Pairs: Right The forefoot</span>
                </div>
                <button class="view-more-btn">{{ section.settings.view_more_text }}</button>
              </div>
            </div>
            
            <div class="order-actions">
              <div class="reorder-section">
                <button class="reorder-btn" data-order-id="UP-000F">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M8 2L6.5 3.5L9 6H2V8H9L6.5 10.5L8 12L13 7L8 2Z" fill="currentColor"/>
                  </svg>
                  {{ section.settings.reorder_text }} Shift+2
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</div>

<style>
.order-history-wrapper {
  background: #f8f9fa;
  min-height: 70vh;
}

.order-history-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.order-history-header {
  text-align: center;
  margin-bottom: 40px;
}

.order-history-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.order-history-subtitle {
  font-size: 16px;
  color: #666;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
}

.order-number {
  display: flex;
  flex-direction: column;
}

.order-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.order-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.order-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.order-toggle:hover {
  background: #f8f9fa;
}

.order-toggle svg {
  transition: transform 0.2s ease;
}

.order-toggle[aria-expanded="true"] svg {
  transform: rotate(180deg);
}

.order-content {
  padding: 20px;
  display: none;
}

.order-content.expanded {
  display: block;
}

.order-product {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.product-image img {
  border-radius: 8px;
}

.product-details {
  flex: 1;
}

.product-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.product-variants {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #ddd;
}

.product-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.view-more-btn {
  background: none;
  border: none;
  color: #007bff;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

.order-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.order-offer {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.offer-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.offer-code {
  font-size: 12px;
  color: #666;
}

.buy-again-btn, .reorder-btn {
  background: #00BFFF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.buy-again-btn:hover, .reorder-btn:hover {
  background: #0099CC;
}

.reorder-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .order-history-wrapper {
    padding-top: {{ section.settings.spacing_mobile }}px;
    padding-bottom: {{ section.settings.spacing_mobile }}px;
  }

  .order-history-title {
    font-size: 24px;
  }

  .order-product {
    flex-direction: column;
    text-align: center;
  }

  .order-actions {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .buy-again-btn, .reorder-btn {
    width: 100%;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Check if user is authenticated via magic link
  const urlParams = new URLSearchParams(window.location.search);
  const authenticated = urlParams.get('authenticated');
  const email = urlParams.get('email') || sessionStorage.getItem('buyAgainEmail');

  if (authenticated === 'true' || email) {
    // User is authenticated, show their orders
    loadCustomerOrders(email);
  }

  // Handle order item toggles
  const orderHeaders = document.querySelectorAll('.order-header');
  orderHeaders.forEach(header => {
    header.addEventListener('click', function() {
      const orderItem = this.closest('.order-item');
      const content = orderItem.querySelector('.order-content');
      const toggle = this.querySelector('.order-toggle');

      const isExpanded = toggle.getAttribute('aria-expanded') === 'true';

      // Close all other order items
      orderHeaders.forEach(otherHeader => {
        if (otherHeader !== header) {
          const otherItem = otherHeader.closest('.order-item');
          const otherContent = otherItem.querySelector('.order-content');
          const otherToggle = otherHeader.querySelector('.order-toggle');

          otherContent.classList.remove('expanded');
          otherToggle.setAttribute('aria-expanded', 'false');
        }
      });

      // Toggle current item
      if (isExpanded) {
        content.classList.remove('expanded');
        toggle.setAttribute('aria-expanded', 'false');
      } else {
        content.classList.add('expanded');
        toggle.setAttribute('aria-expanded', 'true');
      }
    });
  });

  function loadCustomerOrders(email) {
    // In a real implementation, this would fetch orders from Shopify API
    // For now, we'll show the demo orders but could customize based on email
    console.log('Loading orders for:', email);

    // You could make an AJAX call here to fetch real customer orders
    // fetch('/apps/buy-again/orders?email=' + encodeURIComponent(email))
    //   .then(response => response.json())
    //   .then(orders => renderOrders(orders));
  }

  // Handle Buy Again buttons
  function setupBuyAgainButtons() {
    const buyAgainBtns = document.querySelectorAll('.buy-again-btn');
    buyAgainBtns.forEach(btn => {
      btn.addEventListener('click', function() {
        const orderId = this.getAttribute('data-order-id');
        handleBuyAgain(this, orderId);
      });
    });
  }

  function handleBuyAgain(button, orderId) {
    // Show loading state
    const originalText = button.textContent;
    button.textContent = 'Adding to cart...';
    button.disabled = true;

    // Get customer email for the API call
    const email = sessionStorage.getItem('buyAgainEmail');

    // In a real implementation, this would:
    // 1. Fetch the original order details
    // 2. Add all items from that order to the current cart
    // 3. Apply any available discounts

    reorderItems(orderId, email)
      .then(function(response) {
        if (response.success) {
          button.textContent = 'Added to cart!';
          button.style.background = '#28a745';

          // Show cart notification or redirect
          setTimeout(() => {
            window.location.href = '/cart';
          }, 1500);
        } else {
          button.textContent = originalText;
          button.disabled = false;
          alert('Failed to add items to cart. Please try again.');
        }
      })
      .catch(function(error) {
        console.error('Reorder error:', error);
        button.textContent = originalText;
        button.disabled = false;
        alert('Something went wrong. Please try again.');
      });
  }

  function reorderItems(orderId, email) {
    // Simulate API call to reorder items
    return new Promise(function(resolve) {
      setTimeout(function() {
        // In a real implementation, this would:
        // 1. Look up the original order
        // 2. Get all line items
        // 3. Add them to cart via Shopify Ajax API
        // 4. Return success/failure

        resolve({
          success: true,
          cartUrl: '/cart',
          itemsAdded: [
            { title: 'On my feet all day', quantity: 1, variant_id: 'demo-variant' }
          ]
        });
      }, 1500);
    });
  }

  // Initialize buy again buttons
  setupBuyAgainButtons();

  // Handle Reorder buttons
  function setupReorderButtons() {
    const reorderBtns = document.querySelectorAll('.reorder-btn');
    reorderBtns.forEach(btn => {
      btn.addEventListener('click', function() {
        const orderId = this.getAttribute('data-order-id');
        handleReorder(this, orderId);
      });
    });
  }

  function handleReorder(button, orderId) {
    const originalHTML = button.innerHTML;
    button.innerHTML = '<svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M8 2L6.5 3.5L9 6H2V8H9L6.5 10.5L8 12L13 7L8 2Z" fill="currentColor"/></svg> Processing...';
    button.disabled = true;

    const email = sessionStorage.getItem('buyAgainEmail');

    reorderItems(orderId, email)
      .then(function(response) {
        if (response.success) {
          button.innerHTML = '<svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M8 2L6.5 3.5L9 6H2V8H9L6.5 10.5L8 12L13 7L8 2Z" fill="currentColor"/></svg> Reordered!';
          button.style.background = '#28a745';

          setTimeout(() => {
            window.location.href = '/cart';
          }, 1500);
        } else {
          button.innerHTML = originalHTML;
          button.disabled = false;
          alert('Failed to reorder. Please try again.');
        }
      })
      .catch(function(error) {
        console.error('Reorder error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        alert('Something went wrong. Please try again.');
      });
  }

  // Initialize reorder buttons
  setupReorderButtons();

  // Handle keyboard shortcuts
  document.addEventListener('keydown', function(e) {
    if (e.shiftKey && e.key === '@') { // Shift + 2 = @
      const reorderBtn = document.querySelector('.reorder-btn');
      if (reorderBtn && !reorderBtn.disabled) {
        reorderBtn.click();
      }
    }
  });

  // Handle view more buttons
  const viewMoreBtns = document.querySelectorAll('.view-more-btn');
  viewMoreBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const orderItem = this.closest('.order-item');
      const orderId = orderItem.getAttribute('data-order-id');

      // In a real implementation, this could show a modal with full order details
      // or expand the current item to show more information
      alert('View more details for order: ' + orderId);
    });
  });

  // Auto-expand first order if coming from magic link
  if (authenticated === 'true' && orderHeaders.length > 0) {
    setTimeout(function() {
      orderHeaders[0].click();
    }, 500);
  }
});
</script>

{% schema %}
{
  "name": "Order History",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Order History"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Choose the order you wish to buy again"
    },
    {
      "type": "text",
      "id": "no_orders_title",
      "label": "No Orders Title",
      "default": "No Orders Found"
    },
    {
      "type": "textarea",
      "id": "no_orders_message",
      "label": "No Orders Message",
      "default": "We couldn't find any orders associated with this email address."
    },
    {
      "type": "text",
      "id": "buy_again_text",
      "label": "Buy Again Button Text",
      "default": "BUY AGAIN"
    },
    {
      "type": "text",
      "id": "view_more_text",
      "label": "View More Text",
      "default": "View more"
    },
    {
      "type": "text",
      "id": "reorder_text",
      "label": "Reorder Button Text",
      "default": "Re-order"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
