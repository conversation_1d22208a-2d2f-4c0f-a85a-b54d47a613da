<div class="buy-again-wrapper" style="padding: 60px 20px; text-align: center; background: #f8f9fa;">
  <div style="max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
    <h1 style="color: #333; margin-bottom: 10px;">{{ section.settings.title | default: 'Welcome back!' }}</h1>
    <p style="color: #666; margin-bottom: 30px;">{{ section.settings.subtitle | default: 'Enter your email below to get a login link' }}</p>
    
    <form id="buyAgainForm" style="margin-bottom: 20px;">
      <input 
        type="email" 
        name="email" 
        placeholder="Your email"
        style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 8px; margin-bottom: 20px; font-size: 16px;"
        required
      >
      <button 
        type="submit"
        style="width: 100%; background: #00BFFF; color: white; border: none; padding: 15px; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer;"
      >
        {{ section.settings.button_text | default: 'SEND ME A LINK' }}
      </button>
    </form>
    
    <div id="message" style="display: none; padding: 12px; border-radius: 6px; margin-top: 20px;"></div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('buyAgainForm');
  const message = document.getElementById('message');
  
  if (form) {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      const email = this.email.value;
      
      if (email) {
        message.style.display = 'block';
        message.style.background = '#d4edda';
        message.style.color = '#155724';
        message.style.border = '1px solid #c3e6cb';
        message.textContent = 'Check your email for a login link!';
        
        setTimeout(function() {
          window.location.href = '/pages/order-history?email=' + encodeURIComponent(email);
        }, 2000);
      }
    });
  }
});
</script>

{% schema %}
{
  "name": "Buy Again Simple",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Welcome back!"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Enter your email below to get a login link"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SEND ME A LINK"
    }
  ]
}
{% endschema %}
