<div class="order-history-wrapper" style="padding: 60px 20px; background: #f8f9fa;">
  <div style="max-width: 800px; margin: 0 auto;">
    <div style="text-align: center; margin-bottom: 40px;">
      <h1 style="color: #333; font-size: 32px; margin-bottom: 10px;">{{ section.settings.title | default: 'Order History' }}</h1>
      <p style="color: #666; font-size: 16px;">{{ section.settings.subtitle | default: 'Choose the order you wish to buy again' }}</p>
    </div>

    <div class="orders-list">
      <!-- Order 1 -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #e9ecef; cursor: pointer;" onclick="toggleOrder('order1')">
          <div>
            <div style="font-size: 12px; color: #666; text-transform: uppercase; letter-spacing: 0.5px;">Order number</div>
            <div style="font-size: 16px; font-weight: 600; color: #333;">UP-000098782</div>
          </div>
          <div style="font-size: 20px; color: #666;">▼</div>
        </div>
        
        <div id="order1" style="display: none; padding: 20px;">
          <div style="display: flex; gap: 15px; margin-bottom: 20px; align-items: center;">
            <div style="width: 60px; height: 60px; background: #FF6B35; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">👟</div>
            
            <div style="flex: 1;">
              <h3 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 10px;">On my feet all day</h3>
              <div style="display: flex; gap: 8px; margin-bottom: 10px;">
                <span style="width: 16px; height: 16px; background: #4A90E2; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #F5A623; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #7ED321; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #333; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
              </div>
              <div style="display: flex; gap: 15px; font-size: 14px; color: #666;">
                <span>Size: 8.5</span>
                <span>Pairs: Right The forefoot</span>
              </div>
            </div>
          </div>
          
          <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 15px; border-top: 1px solid #e9ecef;">
            <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 10px; border-radius: 8px; border: 1px solid #ffeaa7;">
              <div style="font-size: 14px; color: #333; font-weight: 500;">🎉 Secret offer! Up to 75% off</div>
              <div style="font-size: 12px; color: #666;">Use code: Secret</div>
            </div>
            <button onclick="buyAgain('UP-000098782')" style="background: #00BFFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer;">
              {{ section.settings.buy_again_text | default: 'BUY AGAIN' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Order 2 -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #e9ecef; cursor: pointer;" onclick="toggleOrder('order2')">
          <div>
            <div style="font-size: 12px; color: #666; text-transform: uppercase; letter-spacing: 0.5px;">Order number</div>
            <div style="font-size: 16px; font-weight: 600; color: #333;">UP-000098742</div>
          </div>
          <div style="font-size: 20px; color: #666;">▼</div>
        </div>
        
        <div id="order2" style="display: none; padding: 20px;">
          <div style="display: flex; gap: 15px; margin-bottom: 20px; align-items: center;">
            <div style="width: 60px; height: 60px; background: #FF6B35; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">👟</div>
            
            <div style="flex: 1;">
              <h3 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 10px;">On my feet all day</h3>
              <div style="display: flex; gap: 8px; margin-bottom: 10px;">
                <span style="width: 16px; height: 16px; background: #4A90E2; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #F5A623; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #7ED321; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #333; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
              </div>
              <div style="display: flex; gap: 15px; font-size: 14px; color: #666;">
                <span>Size: 8.5</span>
                <span>Pairs: Right The forefoot</span>
              </div>
            </div>
          </div>
          
          <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 15px; border-top: 1px solid #e9ecef;">
            <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 10px; border-radius: 8px; border: 1px solid #ffeaa7;">
              <div style="font-size: 14px; color: #333; font-weight: 500;">🎉 Secret offer! Up to 75% off</div>
              <div style="font-size: 12px; color: #666;">Use code: Secret</div>
            </div>
            <button onclick="buyAgain('UP-000098742')" style="background: #00BFFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer;">
              {{ section.settings.buy_again_text | default: 'BUY AGAIN' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Order 3 with Reorder -->
      <div style="background: white; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; border-bottom: 1px solid #e9ecef; cursor: pointer;" onclick="toggleOrder('order3')">
          <div>
            <div style="font-size: 12px; color: #666; text-transform: uppercase; letter-spacing: 0.5px;">Order number</div>
            <div style="font-size: 16px; font-weight: 600; color: #333;">UP-000F</div>
          </div>
          <div style="font-size: 20px; color: #666;">▼</div>
        </div>
        
        <div id="order3" style="display: none; padding: 20px;">
          <div style="display: flex; gap: 15px; margin-bottom: 20px; align-items: center;">
            <div style="width: 60px; height: 60px; background: #FF6B35; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">👟</div>
            
            <div style="flex: 1;">
              <h3 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 10px;">On my feet all day</h3>
              <div style="display: flex; gap: 8px; margin-bottom: 10px;">
                <span style="width: 16px; height: 16px; background: #4A90E2; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #F5A623; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #7ED321; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
                <span style="width: 16px; height: 16px; background: #333; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></span>
              </div>
              <div style="display: flex; gap: 15px; font-size: 14px; color: #666;">
                <span>Size: 8.5</span>
                <span>Pairs: Right The forefoot</span>
              </div>
            </div>
          </div>
          
          <div style="display: flex; justify-content: center; padding-top: 15px; border-top: 1px solid #e9ecef;">
            <button onclick="reorder('UP-000F')" style="background: #00BFFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 8px;">
              <span>→</span> {{ section.settings.reorder_text | default: 'Re-order' }} Shift+2
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleOrder(orderId) {
  const order = document.getElementById(orderId);
  if (order.style.display === 'none') {
    order.style.display = 'block';
  } else {
    order.style.display = 'none';
  }
}

function buyAgain(orderId) {
  const button = event.target;
  button.textContent = 'Adding to cart...';
  button.disabled = true;
  
  setTimeout(() => {
    button.textContent = 'Added to cart!';
    button.style.background = '#28a745';
    
    setTimeout(() => {
      window.location.href = '/cart';
    }, 1500);
  }, 1000);
}

function reorder(orderId) {
  const button = event.target;
  button.innerHTML = '<span>→</span> Processing...';
  button.disabled = true;
  
  setTimeout(() => {
    button.innerHTML = '<span>→</span> Reordered!';
    button.style.background = '#28a745';
    
    setTimeout(() => {
      window.location.href = '/cart';
    }, 1500);
  }, 1000);
}

// Keyboard shortcut
document.addEventListener('keydown', function(e) {
  if (e.shiftKey && e.key === '@') {
    const reorderBtn = document.querySelector('button[onclick*="reorder"]');
    if (reorderBtn && !reorderBtn.disabled) {
      reorderBtn.click();
    }
  }
});
</script>

{% schema %}
{
  "name": "Order History Simple",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Order History"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Choose the order you wish to buy again"
    },
    {
      "type": "text",
      "id": "buy_again_text",
      "label": "Buy Again Button Text",
      "default": "BUY AGAIN"
    },
    {
      "type": "text",
      "id": "reorder_text",
      "label": "Reorder Button Text",
      "default": "Re-order"
    }
  ]
}
{% endschema %}
