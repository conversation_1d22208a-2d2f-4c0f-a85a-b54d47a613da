# Buy Again Magic Link Functionality

## Обзор

Реализована полная функциональность "Buy Again" с magic link аутентификацией для Shopify темы. Система позволяет клиентам легко повторно заказывать товары через email-ссылку без необходимости входа в аккаунт.

## Созданные файлы

### Templates
- `templates/page.buy-again-magic-link.json` - Шаблон страницы для ввода email
- `templates/page.order-history.json` - Шаблон страницы истории заказов

### Sections
- `sections/main-buy-again-magic-link.liquid` - Секция с формой magic link
- `sections/main-order-history.liquid` - Секция отображения заказов с кнопками Buy Again

### Assets
- `assets/buy-again-styles.css` - Стили для обеих страниц
- `snippets/buy-again-test-helper.liquid` - Помощник для тестирования

## Функциональность

### 1. Magic Link страница (`/pages/buy-again-magic-link`)
- Форма ввода email адреса
- Валидация email
- Отправка magic link (симуляция)
- Редирект на страницу истории заказов

### 2. Order History страница (`/pages/order-history`)
- Отображение списка заказов клиента
- Раскрывающиеся детали заказов
- Кнопки "BUY AGAIN" для повторного заказа
- Кнопки "Re-order" с горячими клавишами
- Специальные предложения и скидки

### 3. JavaScript функциональность
- Обработка форм и валидация
- Управление состоянием через sessionStorage
- Анимации и интерактивность
- Интеграция с Shopify Cart API (готова к подключению)

## Настройка

### Создание страниц в Shopify Admin

1. **Buy Again Magic Link страница:**
   - Перейдите в Online Store > Pages
   - Создайте новую страницу с handle: `buy-again-magic-link`
   - Выберите template: `page.buy-again-magic-link`

2. **Order History страница:**
   - Создайте новую страницу с handle: `order-history`
   - Выберите template: `page.order-history`

### Настройка Customer Accounts

В Shopify Admin > Settings > Customer accounts:
- Включите "Customer accounts" 
- Выберите "Customers can create an account and sign in with a one-time code sent by email"

## Кастомизация

### Настройки секций

Обе секции имеют настраиваемые параметры в Theme Editor:

**Buy Again Magic Link:**
- Title, Subtitle, Description
- Button text
- Success/Error messages
- Spacing (desktop/mobile)

**Order History:**
- Title, Subtitle
- Button texts
- No orders messages
- Spacing (desktop/mobile)

### CSS переменные

В `buy-again-styles.css` определены CSS переменные для легкой кастомизации:

```css
:root {
  --buy-again-primary: #00BFFF;
  --buy-again-primary-hover: #0099CC;
  --buy-again-success: #28a745;
  --buy-again-error: #dc3545;
  /* ... другие переменные */
}
```

## Интеграция с Backend

### Для полной функциональности необходимо:

1. **API endpoint для проверки клиента:**
```javascript
// POST /apps/buy-again/check-customer
{
  "email": "<EMAIL>"
}
```

2. **API endpoint для отправки magic link:**
```javascript
// POST /apps/buy-again/send-magic-link
{
  "email": "<EMAIL>",
  "return_url": "/pages/order-history"
}
```

3. **API endpoint для получения заказов:**
```javascript
// GET /apps/buy-again/orders?email=<EMAIL>&token=magic-token
```

4. **Интеграция с Shopify Cart API:**
```javascript
// Добавление товаров в корзину
fetch('/cart/add.js', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    items: [
      {
        variant_id: 'variant-id',
        quantity: 1,
        properties: {
          'Original Order': 'UP-000098782'
        }
      }
    ]
  })
})
```

## Тестирование

### Использование Test Helper

1. Добавьте в шаблон страницы:
```liquid
{% render 'buy-again-test-helper' %}
```

2. Откройте консоль браузера и выполните:
```javascript
// Полный тест
BuyAgainTestHelper.testCompleteFlow()

// Отдельные тесты
BuyAgainTestHelper.testEmailValidation()
BuyAgainTestHelper.testMagicLinkFlow('<EMAIL>')
BuyAgainTestHelper.showCurrentState()
```

### Тестовые сценарии

1. **Валидация email:**
   - Корректные email адреса
   - Некорректные форматы
   - Пустые поля

2. **Magic Link Flow:**
   - Отправка ссылки
   - Переход по ссылке
   - Валидация токена

3. **Order History:**
   - Загрузка заказов
   - Раскрытие деталей
   - Кнопки Buy Again

4. **Buy Again функциональность:**
   - Добавление в корзину
   - Обработка ошибок
   - Редирект в корзину

## Безопасность

- Magic link токены должны иметь ограниченное время жизни
- Валидация email на backend
- Проверка принадлежности заказов клиенту
- CSRF защита для форм

## Поддержка браузеров

- Современные браузеры (Chrome, Firefox, Safari, Edge)
- Мобильные браузеры
- Graceful degradation для старых браузеров

## Следующие шаги

1. Интеграция с реальным backend API
2. Настройка email уведомлений
3. Добавление аналитики
4. A/B тестирование UI/UX
5. Интеграция с Shopify Customer API

## Поддержка

Для вопросов по интеграции и кастомизации обращайтесь к документации Shopify или разработчику темы.
