{% comment %}
  Buy Again Test Helper
  This snippet helps with testing the buy again functionality
  Usage: {% render 'buy-again-test-helper' %}
{% endcomment %}

<script>
// Test helper functions for Buy Again functionality
window.BuyAgainTestHelper = {
  // Test email validation
  testEmailValidation: function() {
    console.log('Testing email validation...');
    
    const testEmails = [
      '<EMAIL>', // valid
      'invalid-email', // invalid
      'user@domain', // invalid
      '<EMAIL>', // valid
      '<EMAIL>' // valid but no orders
    ];
    
    testEmails.forEach(email => {
      const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      console.log(`${email}: ${isValid ? 'VALID' : 'INVALID'}`);
    });
  },
  
  // Test magic link flow
  testMagicLinkFlow: function(email = '<EMAIL>') {
    console.log('Testing magic link flow for:', email);
    
    // Simulate the flow
    sessionStorage.setItem('buyAgainEmail', email);
    
    // Generate test magic link
    const token = 'test-token-' + Date.now();
    const magicLink = `/pages/buy-again-magic-link?token=${token}&email=${encodeURIComponent(email)}`;
    
    console.log('Generated magic link:', magicLink);
    console.log('Email stored in sessionStorage:', sessionStorage.getItem('buyAgainEmail'));
    
    return magicLink;
  },
  
  // Test order history loading
  testOrderHistoryLoading: function() {
    console.log('Testing order history loading...');
    
    const email = sessionStorage.getItem('buyAgainEmail') || '<EMAIL>';
    console.log('Loading orders for email:', email);
    
    // Simulate order data
    const mockOrders = [
      {
        id: 'UP-000098782',
        name: 'UP-000098782',
        created_at: new Date().toISOString(),
        line_items: [
          {
            title: 'On my feet all day',
            variant: { option1: '8.5', option2: 'Right The forefoot' },
            image: { src: 'placeholder-image.jpg' }
          }
        ]
      }
    ];
    
    console.log('Mock orders:', mockOrders);
    return mockOrders;
  },
  
  // Test buy again functionality
  testBuyAgain: function(orderId = 'UP-000098782') {
    console.log('Testing buy again for order:', orderId);
    
    // Simulate adding items to cart
    const cartItems = [
      {
        variant_id: 'demo-variant-id',
        quantity: 1,
        properties: {
          'Original Order': orderId,
          'Reorder Date': new Date().toISOString()
        }
      }
    ];
    
    console.log('Items to add to cart:', cartItems);
    
    // In a real implementation, this would use Shopify's Ajax API:
    // fetch('/cart/add.js', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ items: cartItems })
    // })
    
    return Promise.resolve({ success: true, items: cartItems });
  },
  
  // Test complete flow
  testCompleteFlow: function() {
    console.log('=== Testing Complete Buy Again Flow ===');
    
    // Step 1: Email validation
    this.testEmailValidation();
    
    // Step 2: Magic link generation
    const magicLink = this.testMagicLinkFlow('<EMAIL>');
    
    // Step 3: Order history loading
    const orders = this.testOrderHistoryLoading();
    
    // Step 4: Buy again functionality
    this.testBuyAgain(orders[0].id).then(result => {
      console.log('Buy again result:', result);
      console.log('=== Test Complete ===');
    });
  },
  
  // Clear test data
  clearTestData: function() {
    sessionStorage.removeItem('buyAgainEmail');
    sessionStorage.removeItem('buyAgainToken');
    console.log('Test data cleared');
  },
  
  // Show current state
  showCurrentState: function() {
    console.log('=== Current Buy Again State ===');
    console.log('Email in storage:', sessionStorage.getItem('buyAgainEmail'));
    console.log('Token in storage:', sessionStorage.getItem('buyAgainToken'));
    console.log('Current URL:', window.location.href);
    console.log('URL Parameters:', Object.fromEntries(new URLSearchParams(window.location.search)));
  }
};

// Auto-run tests in development
if (window.location.hostname === 'localhost' || window.location.hostname.includes('ngrok')) {
  console.log('Buy Again Test Helper loaded. Use BuyAgainTestHelper.testCompleteFlow() to run tests.');
}
</script>

{% comment %}
  Usage instructions:
  
  1. Add this snippet to your page templates for testing:
     {% render 'buy-again-test-helper' %}
  
  2. Open browser console and run:
     BuyAgainTestHelper.testCompleteFlow()
  
  3. Individual test functions:
     - BuyAgainTestHelper.testEmailValidation()
     - BuyAgainTestHelper.testMagicLinkFlow('<EMAIL>')
     - BuyAgainTestHelper.testOrderHistoryLoading()
     - BuyAgainTestHelper.testBuyAgain('ORDER-ID')
     - BuyAgainTestHelper.showCurrentState()
     - BuyAgainTestHelper.clearTestData()
{% endcomment %}
